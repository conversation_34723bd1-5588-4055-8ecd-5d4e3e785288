# 🚀 AI Pipeline Generator - Streamlit App

A comprehensive Streamlit application that generates production-ready data engineering pipelines from natural language descriptions using AI agents, with complete package download and one-click local deployment capabilities.

## 🌟 Features

### 🤖 AI-Powered Pipeline Generation
- **Natural Language Input**: Describe your pipeline requirements in plain English
- **Multi-Agent System**: Leverages AutoGen with specialized AI agents
- **Three Pipeline Types**:
  - **Standard Multi-Agent**: Flexible pipelines with component selection
  - **Enterprise Grade**: Production-ready with monitoring, security, and scaling
  - **Real Deployable**: Complete systems with one-command deployment

### 📦 Complete Package Creation
- **Full Pipeline Packages**: Everything needed for deployment
- **Multiple Formats**: Standard, Enterprise, and Real Deployable packages
- **Includes**:
  - Main pipeline code
  - Configuration files
  - Docker setup
  - Kubernetes manifests
  - Deployment scripts
  - Documentation
  - Requirements and dependencies

### 🚀 One-Click Local Deployment
- **Automated Deployment**: Deploy generated pipelines locally with one click
- **Docker Integration**: Automatic containerization and orchestration
- **Health Monitoring**: Real-time deployment status and health checks
- **Service Management**: Start, stop, and monitor deployed services

### 📊 Real-Time Monitoring
- **Live Status Updates**: Track generation and deployment progress
- **Deployment Logs**: View real-time deployment logs
- **Service Health**: Monitor running services and endpoints
- **Metrics Integration**: Built-in Prometheus metrics

## 🏗️ Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    Streamlit Web Interface                  │
├─────────────────────────────────────────────────────────────┤
│  Natural Language Input  │  Pipeline Configuration         │
│  Progress Tracking       │  Status Monitoring              │
│  Download Management     │  Deployment Controls            │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                   AI Agent Pipeline System                  │
├─────────────────────────────────────────────────────────────┤
│  Multi-Agent System      │  Enterprise Generator           │
│  Real Pipeline Generator │  Component Selection            │
│  Configuration Manager   │  Code Generation                │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                    Pipeline Packager                        │
├─────────────────────────────────────────────────────────────┤
│  Package Creation        │  File Organization              │
│  Dependency Management   │  Documentation Generation       │
│  Docker Configuration    │  Kubernetes Manifests          │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                   Deployment Manager                        │
├─────────────────────────────────────────────────────────────┤
│  Local Deployment        │  Docker Orchestration           │
│  Health Monitoring       │  Service Management             │
│  Log Streaming          │  Status Reporting               │
└─────────────────────────────────────────────────────────────┘
```

## 🚀 Quick Start

### 1. Installation

```bash
# Clone the repository
git clone <repository-url>
cd data_Eng_db-main

# Install dependencies
python launch_pipeline_app.py --install

# Setup environment
python launch_pipeline_app.py --setup
```

### 2. Launch the App

```bash
# Quick launch
python launch_pipeline_app.py

# Custom port
python launch_pipeline_app.py --port 8502

# Debug mode
python launch_pipeline_app.py --debug
```

### 3. Access the App

Open your browser and navigate to: `http://localhost:8501`

## 📋 Usage Guide

### Step 1: Describe Your Pipeline
1. Open the app in your browser
2. In the main text area, describe your pipeline requirements in natural language
3. Be as detailed as possible about:
   - Data sources and formats
   - Processing requirements
   - Storage needs
   - API requirements
   - Deployment preferences

**Example Description:**
```
I want to build a pipeline that:
- Processes PDF documents from a folder
- Extracts text and creates embeddings using OpenAI
- Stores embeddings in Pinecone vector database
- Provides a FastAPI-based RAG question answering service
- Includes Redis caching for performance
- Has monitoring with Prometheus and Grafana
- Can be deployed with Docker Compose
```

### Step 2: Configure Pipeline Type
1. In the sidebar, select your pipeline type:
   - **Standard Multi-Agent**: For flexible, component-based pipelines
   - **Enterprise Grade**: For production systems with full enterprise features
   - **Real Deployable**: For complete, production-ready systems

2. Configure advanced options based on your selection

### Step 3: Generate Pipeline
1. Click "🚀 Generate Pipeline"
2. Watch the real-time progress as AI agents work
3. Review the generated code, configuration, and plan

### Step 4: Create Package
1. Click "📦 Create Package" to bundle everything
2. The package includes all necessary files for deployment
3. Download the complete package ZIP file

### Step 5: Deploy Locally
1. Click "🚀 Deploy Locally" for one-click deployment
2. Monitor deployment progress in real-time
3. Access your deployed pipeline through provided endpoints

## 🔧 Configuration

### Environment Variables

Create a `.env` file or set environment variables:

```bash
# Azure OpenAI Configuration
AZURE_OPENAI_ENDPOINT=https://your-endpoint.openai.azure.com/
AZURE_OPENAI_KEY=your-api-key
AZURE_OPENAI_MODEL=gpt-4o

# Redis Configuration
REDIS_URL=redis://localhost:6379/0

# Application Configuration
HOST=0.0.0.0
PORT=8000
```

### Pipeline Types Configuration

#### Standard Multi-Agent
- PDF Parsing: PyMuPDF, Unstructured, LlamaParse
- Chunking: Recursive, Semantic, Fixed-size
- Embeddings: OpenAI, Cohere, Jina
- Vector Stores: Pinecone, Qdrant, Weaviate, ChromaDB
- LLMs: OpenAI, Azure OpenAI, Groq

#### Enterprise Grade
- Architecture: Microservices, Monolithic, Serverless
- Databases: PostgreSQL, MongoDB, Redis
- Message Queues: Redis, RabbitMQ, Apache Kafka
- Monitoring: Prometheus, Grafana, ELK Stack
- Security: Authentication, Authorization, Encryption

#### Real Deployable
- Pre-configured production settings
- Complete Docker and Kubernetes setup
- Monitoring and logging included
- CI/CD pipeline templates

## 📦 Package Contents

Generated packages include:

```
generated_pipeline/
├── src/
│   └── main_pipeline.py          # Main application code
├── infrastructure/
│   ├── Dockerfile                # Container configuration
│   ├── docker-compose.yml        # Multi-service setup
│   └── k8s/                      # Kubernetes manifests
├── monitoring/
│   ├── prometheus.yml            # Metrics configuration
│   └── grafana/                  # Dashboard configs
├── security/
│   └── auth.py                   # Authentication setup
├── tests/
│   └── test_pipeline.py          # Test suite
├── docs/
│   └── README.md                 # Documentation
├── requirements.txt              # Dependencies
├── config.env                    # Configuration template
├── deploy.sh                     # Deployment script
└── package_metadata.json         # Package information
```

## 🚀 Deployment Options

### Local Development
```bash
# Extract package and run
unzip generated_pipeline.zip
cd generated_pipeline
./deploy.sh
```

### Docker Compose
```bash
docker-compose up -d
```

### Kubernetes
```bash
kubectl apply -f k8s/
```

## 📊 Monitoring and Management

### Available Endpoints
- **Main Application**: `http://localhost:8000`
- **API Documentation**: `http://localhost:8000/docs`
- **Health Check**: `http://localhost:8000/health`
- **Metrics**: `http://localhost:8000/metrics`
- **Prometheus**: `http://localhost:9090`
- **Grafana**: `http://localhost:3000`

### Management Commands
```bash
# Check deployment status
python launch_pipeline_app.py --check

# Stop all services
docker-compose down

# View logs
docker-compose logs -f
```

## 🛠️ Troubleshooting

### Common Issues

1. **Dependencies Missing**
   ```bash
   python launch_pipeline_app.py --install
   ```

2. **Docker Not Available**
   - Install Docker Desktop
   - Ensure Docker daemon is running

3. **Port Conflicts**
   ```bash
   python launch_pipeline_app.py --port 8502
   ```

4. **Azure OpenAI Issues**
   - Check API key and endpoint
   - Verify model availability

### Debug Mode
```bash
python launch_pipeline_app.py --debug
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions:
- Check the troubleshooting section
- Review the logs in debug mode
- Open an issue on GitHub

---

**Built with ❤️ using Streamlit, AutoGen, and enterprise-grade AI agents**
