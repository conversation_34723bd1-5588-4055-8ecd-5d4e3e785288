#!/usr/bin/env python3
"""
Demo Script for Streamlit Pipeline App
Tests the pipeline generation and packaging functionality
"""

import asyncio
import tempfile
import shutil
from pathlib import Path

# Import our modules
from multi_agent_pipeline_system import DataPipelineAgentSystem
from real_working_pipeline import RealWorkingPipelineGenerator
from pipeline_packager import PipelinePackager
from deployment_manager import DeploymentManager

def demo_standard_pipeline():
    """Demo the standard multi-agent pipeline generation."""
    
    print("🤖 DEMO: Standard Multi-Agent Pipeline")
    print("=" * 50)
    
    # Initialize system
    system = DataPipelineAgentSystem()
    
    # Test requirements
    requirements = """
    I want to build a pipeline that processes PDF documents and creates a RAG system.
    The pipeline should:
    - Extract text from PDF files
    - Create embeddings using OpenAI
    - Store in a vector database
    - Provide a question-answering API
    """
    
    print(f"📋 Requirements: {requirements}")
    print("\n🚀 Generating pipeline...")
    
    try:
        result = system.create_pipeline(requirements)
        
        print("✅ Pipeline generated successfully!")
        print(f"📁 Components used: {result['components_used']}")
        print(f"📄 Code length: {len(result['pipeline_code'])} characters")
        
        return result
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

async def demo_real_deployable_pipeline():
    """Demo the real deployable pipeline generation."""
    
    print("\n🚀 DEMO: Real Deployable Pipeline")
    print("=" * 50)
    
    # Initialize generator
    generator = RealWorkingPipelineGenerator()
    
    # Test requirements
    requirements = "Enterprise RAG system with Azure OpenAI, Redis caching, monitoring, and ONE-COMMAND deployment"
    
    print(f"📋 Requirements: {requirements}")
    print("\n🚀 Generating real pipeline...")
    
    try:
        result = await generator.generate_complete_pipeline(requirements)
        
        print("✅ Real pipeline generated successfully!")
        print(f"📁 Output directory: {result['output_directory']}")
        print(f"🚀 Deployment command: {result['deployment_command']}")
        
        return result
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

def demo_pipeline_packaging():
    """Demo the pipeline packaging functionality."""
    
    print("\n📦 DEMO: Pipeline Packaging")
    print("=" * 50)
    
    # Generate a standard pipeline first
    system = DataPipelineAgentSystem()
    requirements = "Simple PDF processing pipeline with embeddings"
    
    print("🚀 Generating pipeline for packaging...")
    pipeline_result = system.create_pipeline(requirements)
    
    if not pipeline_result:
        print("❌ Failed to generate pipeline for packaging demo")
        return None
    
    # Initialize packager
    packager = PipelinePackager()
    
    print("📦 Creating package...")
    
    try:
        # Test different package types
        package_types = ["standard", "enterprise"]
        
        for package_type in package_types:
            print(f"\n📦 Creating {package_type} package...")
            
            package_path = packager.create_complete_package(
                pipeline_result,
                requirements,
                package_type
            )
            
            print(f"✅ {package_type.title()} package created: {package_path}")
            
            # Check package contents
            import zipfile
            with zipfile.ZipFile(package_path, 'r') as zipf:
                files = zipf.namelist()
                print(f"📁 Package contains {len(files)} files:")
                for file in files[:5]:  # Show first 5 files
                    print(f"   - {file}")
                if len(files) > 5:
                    print(f"   ... and {len(files) - 5} more files")
        
        return package_path
        
    except Exception as e:
        print(f"❌ Packaging error: {e}")
        return None
    finally:
        # Cleanup
        packager.cleanup()

def demo_deployment_manager():
    """Demo the deployment manager functionality."""
    
    print("\n🚀 DEMO: Deployment Manager")
    print("=" * 50)
    
    # Initialize deployment manager
    deployment_manager = DeploymentManager()
    
    print("🔍 Checking prerequisites...")
    
    # Check prerequisites
    prereq_result = deployment_manager._check_prerequisites()
    
    if prereq_result["success"]:
        print("✅ Prerequisites check passed")
    else:
        print(f"⚠️ Prerequisites check failed: {prereq_result['error']}")
        print("💡 Install Docker and Docker Compose for full deployment functionality")
    
    # Test status functionality
    print("\n📊 Testing status functionality...")
    status = deployment_manager.get_deployment_status()
    print(f"📊 Current status: {status['status']}")
    
    return True

def demo_integration_test():
    """Demo the complete integration workflow."""
    
    print("\n🔄 DEMO: Complete Integration Test")
    print("=" * 50)
    
    try:
        # Step 1: Generate pipeline
        print("1️⃣ Generating pipeline...")
        system = DataPipelineAgentSystem()
        requirements = "PDF processing pipeline with vector search"
        pipeline_result = system.create_pipeline(requirements)
        
        if not pipeline_result:
            print("❌ Pipeline generation failed")
            return False
        
        print("✅ Pipeline generated")
        
        # Step 2: Create package
        print("\n2️⃣ Creating package...")
        packager = PipelinePackager()
        package_path = packager.create_complete_package(
            pipeline_result,
            requirements,
            "standard"
        )
        
        print(f"✅ Package created: {package_path}")
        
        # Step 3: Test deployment manager (without actual deployment)
        print("\n3️⃣ Testing deployment manager...")
        deployment_manager = DeploymentManager()
        
        # Just test the status functionality
        status = deployment_manager.get_deployment_status()
        print(f"✅ Deployment manager status: {status['status']}")
        
        print("\n🎉 Integration test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        return False
    finally:
        # Cleanup
        try:
            packager.cleanup()
        except:
            pass

def main():
    """Run all demos."""
    
    print("🚀 STREAMLIT PIPELINE APP DEMO")
    print("=" * 60)
    print("Testing all components of the pipeline generation system")
    print("=" * 60)
    
    # Demo 1: Standard pipeline
    demo_standard_pipeline()
    
    # Demo 2: Real deployable pipeline
    asyncio.run(demo_real_deployable_pipeline())
    
    # Demo 3: Pipeline packaging
    demo_pipeline_packaging()
    
    # Demo 4: Deployment manager
    demo_deployment_manager()
    
    # Demo 5: Integration test
    demo_integration_test()
    
    print("\n🎉 ALL DEMOS COMPLETED!")
    print("=" * 60)
    print("🚀 Ready to launch the Streamlit app!")
    print("💡 Run: python launch_pipeline_app.py")
    print("🌐 Then open: http://localhost:8501")

if __name__ == "__main__":
    main()
