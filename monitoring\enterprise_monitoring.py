"""
Enterprise Monitoring and Observability System
Comprehensive monitoring, metrics, alerting, and observability for data pipelines.
"""

import os
import time
import json
import asyncio
from typing import Dict, List, Any, Optional, Union, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import logging
import threading
from collections import defaultdict, deque
import psutil
import redis
from prometheus_client import Counter, Histogram, Gauge, CollectorRegistry, generate_latest
from opentelemetry import trace, metrics
# from opentelemetry.exporter.jaeger.thrift import JaegerExporter
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import BatchSpanProcessor
from opentelemetry.sdk.metrics import MeterProvider
from opentelemetry.sdk.metrics.export import PeriodicExportingMetricReader

class AlertSeverity(Enum):
    """Alert severity levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

@dataclass
class Alert:
    """Alert data structure."""
    id: str
    severity: AlertSeverity
    title: str
    description: str
    timestamp: datetime
    source: str
    metadata: Dict[str, Any] = field(default_factory=dict)
    resolved: bool = False
    resolved_at: Optional[datetime] = None

@dataclass
class MetricThreshold:
    """Metric threshold configuration."""
    metric_name: str
    operator: str  # >, <, >=, <=, ==, !=
    threshold_value: float
    severity: AlertSeverity
    duration_seconds: int = 60  # How long threshold must be breached

class MetricsCollector:
    """Enterprise metrics collection and monitoring."""
    
    def __init__(self, redis_url: str = "redis://localhost:6379"):
        self.registry = CollectorRegistry()
        self.redis_client = redis.from_url(redis_url) if redis_url else None
        
        # Prometheus metrics
        self.execution_time_histogram = Histogram(
            'pipeline_execution_time_seconds',
            'Time spent executing pipeline functions',
            ['function_name', 'status'],
            registry=self.registry
        )
        
        self.success_counter = Counter(
            'pipeline_success_total',
            'Total successful pipeline executions',
            ['function_name'],
            registry=self.registry
        )
        
        self.error_counter = Counter(
            'pipeline_error_total',
            'Total pipeline errors',
            ['function_name', 'error_type'],
            registry=self.registry
        )
        
        self.data_quality_gauge = Gauge(
            'pipeline_data_quality_score',
            'Data quality score for pipeline components',
            ['component', 'metric_type'],
            registry=self.registry
        )
        
        self.throughput_gauge = Gauge(
            'pipeline_throughput_docs_per_second',
            'Pipeline throughput in documents per second',
            ['component'],
            registry=self.registry
        )
        
        self.resource_usage_gauge = Gauge(
            'pipeline_resource_usage',
            'Resource usage metrics',
            ['resource_type', 'component'],
            registry=self.registry
        )
        
        # Internal metrics storage
        self.metrics_buffer = defaultdict(deque)
        self.active_flows = {}
        self.thresholds = []
        
        # Start background monitoring
        self._start_background_monitoring()
    
    def record_execution_time(self, function_name: str, execution_time: float, status: str = "success"):
        """Record function execution time."""
        self.execution_time_histogram.labels(
            function_name=function_name,
            status=status
        ).observe(execution_time)
        
        # Store in buffer for trend analysis
        self.metrics_buffer[f"{function_name}_execution_time"].append({
            "timestamp": datetime.now(),
            "value": execution_time,
            "status": status
        })
    
    def increment_success_counter(self, function_name: str):
        """Increment success counter."""
        self.success_counter.labels(function_name=function_name).inc()
    
    def increment_error_counter(self, function_name: str, error_type: str):
        """Increment error counter."""
        self.error_counter.labels(
            function_name=function_name,
            error_type=error_type
        ).inc()
    
    def record_data_quality_score(self, component: str, score: float, metric_type: str = "overall"):
        """Record data quality score."""
        self.data_quality_gauge.labels(
            component=component,
            metric_type=metric_type
        ).set(score)
        
        # Store for trend analysis
        self.metrics_buffer[f"{component}_quality_{metric_type}"].append({
            "timestamp": datetime.now(),
            "value": score
        })
    
    def record_throughput(self, component: str, docs_per_second: float):
        """Record throughput metrics."""
        self.throughput_gauge.labels(component=component).set(docs_per_second)
        
        self.metrics_buffer[f"{component}_throughput"].append({
            "timestamp": datetime.now(),
            "value": docs_per_second
        })
    
    def record_resource_usage(self, component: str):
        """Record system resource usage."""
        # CPU usage
        cpu_percent = psutil.cpu_percent(interval=1)
        self.resource_usage_gauge.labels(
            resource_type="cpu_percent",
            component=component
        ).set(cpu_percent)
        
        # Memory usage
        memory = psutil.virtual_memory()
        self.resource_usage_gauge.labels(
            resource_type="memory_percent",
            component=component
        ).set(memory.percent)
        
        # Disk usage
        disk = psutil.disk_usage('/')
        self.resource_usage_gauge.labels(
            resource_type="disk_percent",
            component=component
        ).set((disk.used / disk.total) * 100)
    
    def start_flow_monitoring(self, flow_id: str):
        """Start monitoring a flow execution."""
        self.active_flows[flow_id] = {
            "start_time": datetime.now(),
            "status": "running",
            "metrics": {}
        }
    
    def end_flow_monitoring(self, flow_id: str):
        """End monitoring a flow execution."""
        if flow_id in self.active_flows:
            flow_data = self.active_flows[flow_id]
            flow_data["end_time"] = datetime.now()
            flow_data["duration"] = (flow_data["end_time"] - flow_data["start_time"]).total_seconds()
            
            # Store completed flow data
            if self.redis_client:
                self.redis_client.setex(
                    f"flow_metrics:{flow_id}",
                    3600,  # 1 hour TTL
                    json.dumps(flow_data, default=str)
                )
    
    def record_flow_success(self, flow_id: str):
        """Record successful flow completion."""
        if flow_id in self.active_flows:
            self.active_flows[flow_id]["status"] = "success"
    
    def record_flow_error(self, flow_id: str, error_type: str):
        """Record flow error."""
        if flow_id in self.active_flows:
            self.active_flows[flow_id]["status"] = "error"
            self.active_flows[flow_id]["error_type"] = error_type
    
    def add_threshold(self, threshold: MetricThreshold):
        """Add a metric threshold for alerting."""
        self.thresholds.append(threshold)
    
    def get_metrics_export(self) -> str:
        """Get Prometheus metrics export."""
        return generate_latest(self.registry).decode('utf-8')
    
    def get_flow_metrics(self, flow_id: str) -> Optional[Dict[str, Any]]:
        """Get metrics for a specific flow."""
        if self.redis_client:
            data = self.redis_client.get(f"flow_metrics:{flow_id}")
            if data:
                return json.loads(data)
        
        return self.active_flows.get(flow_id)
    
    def get_trend_analysis(self, metric_name: str, hours: int = 24) -> Dict[str, Any]:
        """Get trend analysis for a metric."""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        if metric_name in self.metrics_buffer:
            recent_data = [
                point for point in self.metrics_buffer[metric_name]
                if point["timestamp"] > cutoff_time
            ]
            
            if recent_data:
                values = [point["value"] for point in recent_data]
                return {
                    "metric_name": metric_name,
                    "data_points": len(values),
                    "min_value": min(values),
                    "max_value": max(values),
                    "avg_value": sum(values) / len(values),
                    "trend": "increasing" if values[-1] > values[0] else "decreasing",
                    "recent_data": recent_data[-10:]  # Last 10 points
                }
        
        return {"metric_name": metric_name, "data_points": 0}
    
    def _start_background_monitoring(self):
        """Start background monitoring thread."""
        def monitor():
            while True:
                try:
                    # Check thresholds
                    self._check_thresholds()
                    
                    # Clean old data
                    self._cleanup_old_data()
                    
                    # Record system metrics
                    self.record_resource_usage("system")
                    
                    time.sleep(30)  # Check every 30 seconds
                    
                except Exception as e:
                    logging.error(f"Background monitoring error: {e}")
        
        thread = threading.Thread(target=monitor, daemon=True)
        thread.start()
    
    def _check_thresholds(self):
        """Check metric thresholds and generate alerts."""
        for threshold in self.thresholds:
            # Implementation would check current metric values against thresholds
            # and generate alerts if breached
            pass
    
    def _cleanup_old_data(self):
        """Clean up old metric data."""
        cutoff_time = datetime.now() - timedelta(hours=24)
        
        for metric_name, data_points in self.metrics_buffer.items():
            # Remove old data points
            while data_points and data_points[0]["timestamp"] < cutoff_time:
                data_points.popleft()

class HealthChecker:
    """Enterprise health checking system."""
    
    def __init__(self):
        self.health_checks = {}
        self.last_check_results = {}
    
    def register_health_check(self, name: str, check_function: Callable[[], bool]):
        """Register a health check function."""
        self.health_checks[name] = check_function
    
    def check_data_source_health(self, data_source: str) -> bool:
        """Check if a data source is healthy."""
        # Implementation would check data source connectivity
        return True
    
    def check_vector_store_health(self, vector_store: str) -> bool:
        """Check if vector store is healthy."""
        # Implementation would check vector store connectivity
        return True
    
    def check_llm_health(self, llm_provider: str) -> bool:
        """Check if LLM provider is healthy."""
        # Implementation would check LLM API connectivity
        return True
    
    def run_all_health_checks(self) -> Dict[str, bool]:
        """Run all registered health checks."""
        results = {}
        
        for name, check_function in self.health_checks.items():
            try:
                results[name] = check_function()
            except Exception as e:
                logging.error(f"Health check {name} failed: {e}")
                results[name] = False
        
        self.last_check_results = results
        return results
    
    def get_health_status(self) -> Dict[str, Any]:
        """Get overall health status."""
        results = self.run_all_health_checks()
        
        return {
            "overall_healthy": all(results.values()),
            "checks": results,
            "timestamp": datetime.now().isoformat(),
            "unhealthy_services": [name for name, status in results.items() if not status]
        }

class AlertManager:
    """Enterprise alerting system."""
    
    def __init__(self):
        self.alerts = []
        self.alert_handlers = []
        self.alert_history = deque(maxlen=1000)
    
    def add_alert_handler(self, handler: Callable[[Alert], None]):
        """Add an alert handler function."""
        self.alert_handlers.append(handler)
    
    def create_alert(self, 
                    severity: AlertSeverity,
                    title: str,
                    description: str,
                    source: str,
                    metadata: Optional[Dict[str, Any]] = None) -> Alert:
        """Create a new alert."""
        
        alert = Alert(
            id=f"alert_{int(time.time())}_{len(self.alerts)}",
            severity=severity,
            title=title,
            description=description,
            timestamp=datetime.now(),
            source=source,
            metadata=metadata or {}
        )
        
        self.alerts.append(alert)
        self.alert_history.append(alert)
        
        # Notify handlers
        for handler in self.alert_handlers:
            try:
                handler(alert)
            except Exception as e:
                logging.error(f"Alert handler failed: {e}")
        
        return alert
    
    def resolve_alert(self, alert_id: str):
        """Resolve an alert."""
        for alert in self.alerts:
            if alert.id == alert_id:
                alert.resolved = True
                alert.resolved_at = datetime.now()
                break
    
    def get_active_alerts(self, severity: Optional[AlertSeverity] = None) -> List[Alert]:
        """Get active alerts, optionally filtered by severity."""
        active = [alert for alert in self.alerts if not alert.resolved]
        
        if severity:
            active = [alert for alert in active if alert.severity == severity]
        
        return active
    
    def get_alert_summary(self) -> Dict[str, Any]:
        """Get alert summary statistics."""
        active_alerts = self.get_active_alerts()
        
        severity_counts = defaultdict(int)
        for alert in active_alerts:
            severity_counts[alert.severity.value] += 1
        
        return {
            "total_active": len(active_alerts),
            "by_severity": dict(severity_counts),
            "oldest_alert": min(active_alerts, key=lambda a: a.timestamp).timestamp if active_alerts else None,
            "most_recent": max(active_alerts, key=lambda a: a.timestamp).timestamp if active_alerts else None
        }

# Global instances
metrics_collector = MetricsCollector()
health_checker = HealthChecker()
alert_manager = AlertManager()

# Export for use in other modules
__all__ = [
    'MetricsCollector',
    'HealthChecker', 
    'AlertManager',
    'Alert',
    'AlertSeverity',
    'MetricThreshold',
    'metrics_collector',
    'health_checker',
    'alert_manager'
]
