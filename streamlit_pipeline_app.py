"""
Enhanced Streamlit Pipeline Generator App
Complete pipeline generation with AI agents, download, and local deployment
"""

import streamlit as st
import asyncio
import tempfile
import os
import json
import time
from pathlib import Path
from typing import Dict, Any, Optional
import threading

# Import our custom modules
from multi_agent_pipeline_system import DataPipelineAgentSystem
from real_working_pipeline import RealWorkingPipelineGenerator
from enterprise_pipeline_system import EnterprisePipelineSystem
from pipeline_packager import PipelinePackager
from deployment_manager import DeploymentManager

# Page configuration
st.set_page_config(
    page_title="AI Pipeline Generator",
    page_icon="🚀",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS
st.markdown("""
<style>
    .main-header {
        font-size: 3rem;
        font-weight: bold;
        text-align: center;
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        margin-bottom: 2rem;
    }
    
    .feature-card {
        background: #f8f9fa;
        padding: 1.5rem;
        border-radius: 10px;
        border-left: 4px solid #667eea;
        margin: 1rem 0;
    }
    
    .status-success {
        background: #d4edda;
        color: #155724;
        padding: 0.75rem;
        border-radius: 5px;
        border: 1px solid #c3e6cb;
    }
    
    .status-error {
        background: #f8d7da;
        color: #721c24;
        padding: 0.75rem;
        border-radius: 5px;
        border: 1px solid #f5c6cb;
    }
    
    .status-warning {
        background: #fff3cd;
        color: #856404;
        padding: 0.75rem;
        border-radius: 5px;
        border: 1px solid #ffeaa7;
    }
</style>
""", unsafe_allow_html=True)

# Initialize session state
def init_session_state():
    """Initialize session state variables."""
    if 'pipeline_system' not in st.session_state:
        st.session_state.pipeline_system = DataPipelineAgentSystem()
    
    if 'packager' not in st.session_state:
        st.session_state.packager = PipelinePackager()
    
    if 'deployment_manager' not in st.session_state:
        st.session_state.deployment_manager = DeploymentManager()
    
    if 'generated_pipeline' not in st.session_state:
        st.session_state.generated_pipeline = None
    
    if 'package_path' not in st.session_state:
        st.session_state.package_path = None
    
    if 'deployment_status' not in st.session_state:
        st.session_state.deployment_status = "idle"
    
    if 'deployment_logs' not in st.session_state:
        st.session_state.deployment_logs = []

def main():
    """Main application function."""
    
    init_session_state()
    
    # Header
    st.markdown('<h1 class="main-header">🚀 AI Pipeline Generator</h1>', unsafe_allow_html=True)
    st.markdown("### Generate production-ready data pipelines using AI agents")
    
    # Sidebar
    with st.sidebar:
        st.header("🎛️ Configuration")
        
        # Pipeline type selection
        pipeline_type = st.selectbox(
            "Pipeline Type",
            ["Standard Multi-Agent", "Enterprise Grade", "Real Deployable"],
            help="Choose the type of pipeline to generate"
        )
        
        # Advanced options
        with st.expander("🔧 Advanced Options"):
            if pipeline_type == "Standard Multi-Agent":
                show_advanced_options()
            elif pipeline_type == "Enterprise Grade":
                show_enterprise_options()
            else:
                show_real_deployable_options()
        
        # System status
        st.header("📊 System Status")
        show_system_status()
    
    # Main content area
    col1, col2 = st.columns([2, 1])
    
    with col1:
        # Pipeline description input
        st.header("📝 Describe Your Pipeline")
        
        user_requirements = st.text_area(
            "Natural Language Description",
            placeholder="""Example: I want to build a pipeline that:
- Processes PDF documents from a folder
- Extracts text and creates embeddings
- Stores them in a vector database
- Provides a RAG-based question answering API
- Includes monitoring and logging
- Can be deployed with Docker""",
            height=150,
            help="Describe your pipeline requirements in natural language. Be as detailed as possible."
        )
        
        # Generation controls
        col_gen1, col_gen2, col_gen3 = st.columns(3)
        
        with col_gen1:
            generate_btn = st.button("🚀 Generate Pipeline", type="primary", use_container_width=True)
        
        with col_gen2:
            if st.session_state.generated_pipeline:
                package_btn = st.button("📦 Create Package", use_container_width=True)
            else:
                st.button("📦 Create Package", disabled=True, use_container_width=True)
        
        with col_gen3:
            if st.session_state.package_path:
                deploy_btn = st.button("🚀 Deploy Locally", use_container_width=True)
            else:
                st.button("🚀 Deploy Locally", disabled=True, use_container_width=True)
        
        # Handle generation
        if generate_btn and user_requirements:
            generate_pipeline(user_requirements, pipeline_type)
        
        # Handle packaging
        if 'package_btn' in locals() and package_btn:
            create_pipeline_package(pipeline_type)
        
        # Handle deployment
        if 'deploy_btn' in locals() and deploy_btn:
            deploy_pipeline_locally()
        
        # Display results
        display_pipeline_results()
    
    with col2:
        # Status panel
        st.header("📊 Status Panel")
        display_status_panel()
        
        # Quick actions
        st.header("⚡ Quick Actions")
        display_quick_actions()

def show_advanced_options():
    """Show advanced options for standard multi-agent pipeline."""
    
    options = st.session_state.pipeline_system.get_available_options()
    
    st.selectbox("PDF Parsing Strategy", options["pdf_parsing_strategies"])
    st.selectbox("Chunking Strategy", options["chunking_strategies"])
    st.selectbox("Embedding Provider", options["embedding_providers"])
    st.selectbox("Vector Store", options["vector_stores"])
    st.selectbox("LLM Provider", options["llm_providers"])
    st.multiselect("External Tools", options["external_tools"])

def show_enterprise_options():
    """Show options for enterprise pipeline."""
    
    st.selectbox("Architecture Pattern", ["Microservices", "Monolithic", "Serverless"])
    st.selectbox("Database", ["PostgreSQL", "MongoDB", "Redis"])
    st.selectbox("Message Queue", ["Redis", "RabbitMQ", "Apache Kafka"])
    st.checkbox("Enable Monitoring", value=True)
    st.checkbox("Enable Security", value=True)
    st.checkbox("Enable Auto-scaling", value=True)

def show_real_deployable_options():
    """Show options for real deployable pipeline."""
    
    st.info("Real deployable pipelines come with pre-configured production settings")
    st.selectbox("Deployment Target", ["Docker Compose", "Kubernetes", "Both"])
    st.checkbox("Include Monitoring Stack", value=True)
    st.checkbox("Include CI/CD Pipeline", value=True)

def show_system_status():
    """Show system status in sidebar."""
    
    # Check system components
    status_items = [
        ("Multi-Agent System", "✅ Ready"),
        ("Enterprise Generator", "✅ Ready"),
        ("Real Pipeline Generator", "✅ Ready"),
        ("Package Manager", "✅ Ready"),
        ("Deployment Manager", "✅ Ready")
    ]
    
    for component, status in status_items:
        st.text(f"{component}: {status}")

def generate_pipeline(requirements: str, pipeline_type: str):
    """Generate pipeline based on type and requirements."""
    
    with st.spinner(f"Generating {pipeline_type} pipeline..."):
        progress_bar = st.progress(0)
        status_text = st.empty()
        
        try:
            if pipeline_type == "Standard Multi-Agent":
                status_text.text("🤖 Initializing AI agents...")
                progress_bar.progress(20)
                
                result = st.session_state.pipeline_system.create_pipeline(requirements)
                progress_bar.progress(100)
                
                st.session_state.generated_pipeline = result
                status_text.text("✅ Standard pipeline generated successfully!")
                
            elif pipeline_type == "Enterprise Grade":
                status_text.text("🏢 Generating enterprise components...")
                progress_bar.progress(20)
                
                enterprise_system = EnterprisePipelineSystem()
                result = asyncio.run(enterprise_system.generate_enterprise_pipeline(requirements))
                progress_bar.progress(100)
                
                st.session_state.generated_pipeline = result
                status_text.text("✅ Enterprise pipeline generated successfully!")
                
            elif pipeline_type == "Real Deployable":
                status_text.text("🚀 Creating production-ready pipeline...")
                progress_bar.progress(20)
                
                generator = RealWorkingPipelineGenerator()
                result = asyncio.run(generator.generate_complete_pipeline(requirements))
                progress_bar.progress(100)
                
                st.session_state.generated_pipeline = result
                status_text.text("✅ Real deployable pipeline generated successfully!")
            
            st.success("Pipeline generated successfully! 🎉")
            
        except Exception as e:
            st.error(f"Pipeline generation failed: {str(e)}")
            st.session_state.generated_pipeline = None

def create_pipeline_package(pipeline_type: str):
    """Create downloadable package for the generated pipeline."""
    
    if not st.session_state.generated_pipeline:
        st.error("No pipeline to package!")
        return
    
    with st.spinner("Creating pipeline package..."):
        try:
            package_type_map = {
                "Standard Multi-Agent": "standard",
                "Enterprise Grade": "enterprise", 
                "Real Deployable": "real_deployable"
            }
            
            package_path = st.session_state.packager.create_complete_package(
                st.session_state.generated_pipeline,
                "User requirements",  # You might want to store this
                package_type_map[pipeline_type]
            )
            
            st.session_state.package_path = package_path
            st.success("Package created successfully! 📦")
            
        except Exception as e:
            st.error(f"Package creation failed: {str(e)}")

def deploy_pipeline_locally():
    """Deploy the pipeline package locally."""
    
    if not st.session_state.package_path:
        st.error("No package to deploy!")
        return
    
    # Create deployment status container
    deployment_container = st.container()
    
    def status_callback(status: str, message: str):
        """Callback for deployment status updates."""
        st.session_state.deployment_status = status
        st.session_state.deployment_logs.append(f"[{time.strftime('%H:%M:%S')}] {message}")
    
    with st.spinner("Deploying pipeline locally..."):
        try:
            result = st.session_state.deployment_manager.deploy_pipeline_package(
                st.session_state.package_path,
                status_callback
            )
            
            if result["success"]:
                st.success("Pipeline deployed successfully! 🚀")
                
                # Show endpoints
                if "endpoints" in result:
                    st.info("**Available Endpoints:**")
                    for name, url in result["endpoints"].items():
                        st.write(f"- **{name.title()}**: {url}")
            else:
                st.error(f"Deployment failed: {result['error']}")
                
        except Exception as e:
            st.error(f"Deployment error: {str(e)}")

def display_pipeline_results():
    """Display the generated pipeline results."""
    
    if st.session_state.generated_pipeline:
        st.header("📋 Generated Pipeline")
        
        # Create tabs for different views
        tab1, tab2, tab3, tab4 = st.tabs(["📄 Code Preview", "⚙️ Configuration", "📖 Instructions", "📊 Plan"])
        
        with tab1:
            if "pipeline_code" in st.session_state.generated_pipeline:
                code = st.session_state.generated_pipeline["pipeline_code"]
                st.code(code[:2000] + "..." if len(code) > 2000 else code, language="python")
            else:
                st.info("Code preview not available for this pipeline type")
        
        with tab2:
            if "config_template" in st.session_state.generated_pipeline:
                st.code(st.session_state.generated_pipeline["config_template"], language="bash")
            else:
                st.info("Configuration not available for this pipeline type")
        
        with tab3:
            if "api_instructions" in st.session_state.generated_pipeline:
                st.markdown(st.session_state.generated_pipeline["api_instructions"])
            else:
                st.info("Instructions not available for this pipeline type")
        
        with tab4:
            if "plan" in st.session_state.generated_pipeline:
                st.json(st.session_state.generated_pipeline["plan"])
            else:
                st.info("Plan not available for this pipeline type")
        
        # Download section
        st.header("💾 Downloads")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            if "pipeline_code" in st.session_state.generated_pipeline:
                st.download_button(
                    "📄 Download Code",
                    st.session_state.generated_pipeline["pipeline_code"],
                    file_name="pipeline.py",
                    mime="text/python"
                )
        
        with col2:
            if "config_template" in st.session_state.generated_pipeline:
                st.download_button(
                    "⚙️ Download Config",
                    st.session_state.generated_pipeline["config_template"],
                    file_name="config.env",
                    mime="text/plain"
                )
        
        with col3:
            if st.session_state.package_path:
                with open(st.session_state.package_path, 'rb') as f:
                    st.download_button(
                        "📦 Download Package",
                        f.read(),
                        file_name=Path(st.session_state.package_path).name,
                        mime="application/zip"
                    )

def display_status_panel():
    """Display the status panel."""
    
    # Generation status
    if st.session_state.generated_pipeline:
        st.markdown('<div class="status-success">✅ Pipeline Generated</div>', unsafe_allow_html=True)
    else:
        st.markdown('<div class="status-warning">⏳ No Pipeline Generated</div>', unsafe_allow_html=True)
    
    # Package status
    if st.session_state.package_path:
        st.markdown('<div class="status-success">✅ Package Created</div>', unsafe_allow_html=True)
    else:
        st.markdown('<div class="status-warning">⏳ No Package Created</div>', unsafe_allow_html=True)
    
    # Deployment status
    status_map = {
        "idle": ("⏳ Not Deployed", "status-warning"),
        "extracting": ("📦 Extracting...", "status-warning"),
        "checking": ("🔍 Checking...", "status-warning"),
        "deploying": ("🚀 Deploying...", "status-warning"),
        "waiting": ("⏳ Starting...", "status-warning"),
        "ready": ("✅ Running", "status-success"),
        "error": ("❌ Failed", "status-error"),
        "stopped": ("⏹️ Stopped", "status-warning")
    }
    
    status_text, status_class = status_map.get(st.session_state.deployment_status, ("❓ Unknown", "status-warning"))
    st.markdown(f'<div class="{status_class}">{status_text}</div>', unsafe_allow_html=True)
    
    # Deployment logs
    if st.session_state.deployment_logs:
        with st.expander("📋 Deployment Logs"):
            for log in st.session_state.deployment_logs[-10:]:
                st.text(log)

def display_quick_actions():
    """Display quick action buttons."""
    
    if st.button("🔄 Reset All", use_container_width=True):
        # Reset session state
        for key in ['generated_pipeline', 'package_path', 'deployment_status', 'deployment_logs']:
            if key in st.session_state:
                del st.session_state[key]
        st.rerun()
    
    if st.button("🛑 Stop Deployment", use_container_width=True):
        if st.session_state.deployment_status not in ["idle", "stopped"]:
            result = st.session_state.deployment_manager.stop_deployment()
            if result["success"]:
                st.success("Deployment stopped")
                st.session_state.deployment_status = "stopped"
            else:
                st.error(f"Failed to stop: {result['error']}")
    
    if st.button("📊 View Metrics", use_container_width=True):
        if st.session_state.deployment_status == "ready":
            st.info("Metrics available at: http://localhost:8000/metrics")
        else:
            st.warning("Deploy pipeline first to view metrics")

if __name__ == "__main__":
    main()
